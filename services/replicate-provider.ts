import Replicate from "replicate";
import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";

/**
 * Replicate API 请求类型
 */
export interface ReplicateImageRequest {
  model: string;
  prompt: string;
  options?: {
    // 基础参数
    aspect_ratio?: string;
    image?: string;
    prompt_strength?: number;
    num_outputs?: number;
    num_inference_steps?: number;
    guidance?: number;
    seed?: number;
    output_format?: string;
    output_quality?: number;
    disable_safety_checker?: boolean;
    go_fast?: boolean;
    megapixels?: string;
  };
}

/**
 * Replicate API 响应类型
 */
export interface ReplicateImageResponse {
  id: string;
  status: 'pending' | 'success' | 'failed';
  urls?: string[];
  error?: string;
  usage?: {
    credits_consumed: number;
  };
}

/**
 * Replicate 提供商配置
 */
const REPLICATE_CONFIG = {
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000
};

/**
 * Replicate 提供商实现
 */
export class ReplicateProvider {
  private client: Replicate;

  constructor() {
    const apiToken = process.env.REPLICATE_API_TOKEN;
    if (!apiToken) {
      throw new Error('REPLICATE_API_TOKEN environment variable is required');
    }

    this.client = new Replicate({
      auth: apiToken,
    });
  }

  /**
   * 生成图像
   */
  async generateImage(request: ReplicateImageRequest): Promise<ReplicateImageResponse> {
    const requestId = getUuid();
    
    try {
      console.log(`[Replicate Provider] Starting image generation with request:`, request);

      // 构建输入参数
      const input: any = {
        prompt: request.prompt,
      };

      // 添加可选参数（按照官方文档）
      if (request.options?.aspect_ratio) {
        input.aspect_ratio = request.options.aspect_ratio;
      }
      if (request.options?.image) {
        input.image = request.options.image;
      }
      if (request.options?.prompt_strength !== undefined) {
        input.prompt_strength = request.options.prompt_strength;
      }
      if (request.options?.num_outputs !== undefined) {
        input.num_outputs = request.options.num_outputs;
      }
      if (request.options?.num_inference_steps !== undefined) {
        input.num_inference_steps = request.options.num_inference_steps;
      }
      if (request.options?.guidance !== undefined) {
        input.guidance = request.options.guidance;
      }
      if (request.options?.seed !== undefined) {
        input.seed = request.options.seed;
      }
      if (request.options?.output_format) {
        input.output_format = request.options.output_format;
      }
      if (request.options?.output_quality !== undefined) {
        input.output_quality = request.options.output_quality;
      }
      if (request.options?.disable_safety_checker !== undefined) {
        input.disable_safety_checker = request.options.disable_safety_checker;
      }
      if (request.options?.go_fast !== undefined) {
        input.go_fast = request.options.go_fast;
      }
      if (request.options?.megapixels) {
        input.megapixels = request.options.megapixels;
      }

      console.log(`[Replicate Provider] Calling ${request.model} with input:`, input);

      // 调用 Replicate API
      const output = await this.client.run(request.model as any, { input });

      console.log(`[Replicate Provider] Received output:`, output);

      // 处理输出
      if (Array.isArray(output) && output.length > 0) {
        const urls = output.map(item => {
          if (typeof item === 'string') {
            return item;
          } else if (item && typeof item === 'object' && 'url' in item) {
            return item.url;
          }
          return null;
        }).filter(Boolean) as string[];

        return {
          id: requestId,
          status: 'success',
          urls,
          usage: {
            credits_consumed: this.calculateCredits(request.model)
          }
        };
      } else {
        throw new Error('Invalid output format from Replicate API');
      }

    } catch (error) {
      console.error(`[Replicate Provider] Error generating image:`, error);
      
      return {
        id: requestId,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        usage: {
          credits_consumed: 0
        }
      };
    }
  }

  /**
   * 计算积分消耗
   */
  private calculateCredits(model: string): number {
    // 根据模型计算积分消耗
    switch (model) {
      case 'black-forest-labs/flux-krea-dev':
        return 25; // 每张图片 25 积分
      default:
        return 20; // 默认 20 积分
    }
  }

  /**
   * 检查模型是否支持
   */
  isModelSupported(model: string): boolean {
    const supportedModels = [
      'black-forest-labs/flux-krea-dev'
    ];
    return supportedModels.includes(model);
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[] {
    return [
      'black-forest-labs/flux-krea-dev'
    ];
  }
}

/**
 * 导出单例实例
 */
export const replicateProvider = new ReplicateProvider();
