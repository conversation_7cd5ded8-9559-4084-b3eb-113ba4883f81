-- ========================================
-- 快速启动脚本（精简版）
-- 仅包含核心功能，适合快速原型开发
-- ========================================

-- 核心用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    nickname VARCHAR(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en',
    UNIQUE (email)
);

-- 积分系统
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    model_id VARCHAR(100),
    request_id VARCHAR(255)
);

-- AI 模型表
CREATE TABLE ai_models (
    id SERIAL PRIMARY KEY,
    model_id VARCHAR(100) UNIQUE NOT NULL,
    model_name VARCHAR(200) NOT NULL,
    model_type VARCHAR(50) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    api_endpoint VARCHAR(200) NOT NULL,
    credits_per_unit INT NOT NULL,
    unit_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    description_i18n JSONB,
    model_name_i18n JSONB,
    supported_features JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI 模型使用记录
CREATE TABLE ai_model_usage (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    request_id VARCHAR(255) UNIQUE NOT NULL,
    credits_consumed INT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    request_params JSONB,
    response_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 基础索引
CREATE INDEX idx_users_uuid ON users(uuid);
CREATE INDEX idx_credits_user_uuid ON credits(user_uuid);
CREATE INDEX idx_ai_models_active ON ai_models(is_active);
CREATE INDEX idx_ai_model_usage_user ON ai_model_usage(user_uuid);

-- 核心AI模型数据（仅包含最常用的模型）
INSERT INTO ai_models (
  model_id, model_name, model_type, provider, api_endpoint, credits_per_unit, unit_type,
  description_i18n, model_name_i18n, supported_features, is_active
) VALUES
-- 文本模型
('gemini-2.5-flash', 'Gemini 2.5 Flash', 'text', 'grsai', '/api/ai/generate', 5, 'tokens',
 '{"en": "Fast conversational model", "zh": "快速对话模型"}',
 '{"en": "Gemini 2.5 Flash", "zh": "Gemini 2.5 闪电版"}',
 '["text_generation", "conversation"]', true),

('gpt-4o-mini', 'GPT-4o Mini', 'text', 'grsai', '/api/ai/generate', 8, 'tokens',
 '{"en": "Balanced performance and cost", "zh": "性能与成本平衡"}',
 '{"en": "GPT-4o Mini", "zh": "GPT-4o 迷你版"}',
 '["text_generation", "reasoning"]', true),

-- 多模态模型
('gpt-4o-all', 'GPT-4o All', 'multimodal', 'grsai', '/api/ai/generate', 20, 'tokens',
 '{"en": "Complete GPT-4o with multimodal support", "zh": "GPT-4o 完整版本，支持多模态"}',
 '{"en": "GPT-4o All", "zh": "GPT-4o 全功能版"}',
 '["text_generation", "vision", "multimodal"]', true),

-- 图像生成
('flux-pro-1.1', 'Flux Pro 1.1', 'image', 'grsai', '/api/ai/generate', 30, 'images',
 '{"en": "Professional image generation", "zh": "专业图像生成"}',
 '{"en": "Flux Pro 1.1", "zh": "Flux 专业版 1.1"}',
 '["image_generation", "professional"]', true),

-- 视频生成
('veo3-fast', 'Veo3 Fast', 'video', 'grsai', '/api/ai/generate', 100, 'videos',
 '{"en": "Fast video generation", "zh": "快速视频生成"}',
 '{"en": "Veo3 Fast", "zh": "Veo3 快速版"}',
 '["video_generation", "fast"]', true);

-- 本地化函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  IF content IS NULL THEN RETURN ''; END IF;
  IF content ? locale THEN RETURN content ->> locale; END IF;
  IF content ? fallback THEN RETURN content ->> fallback; END IF;
  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 成本计算函数
CREATE OR REPLACE FUNCTION calculate_model_cost(
    p_model_id VARCHAR(100),
    p_input_size INT,
    p_output_size INT DEFAULT NULL
) RETURNS INT AS $$
DECLARE
    model_config RECORD;
    total_cost INT;
BEGIN
    SELECT credits_per_unit, unit_type INTO model_config
    FROM ai_models WHERE model_id = p_model_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Model % not found', p_model_id;
    END IF;
    
    CASE model_config.unit_type
        WHEN 'tokens' THEN
            total_cost := CEIL((COALESCE(p_input_size, 0) + COALESCE(p_output_size, 0)) / 1000.0) * model_config.credits_per_unit;
        ELSE
            total_cost := model_config.credits_per_unit;
    END CASE;
    
    RETURN GREATEST(total_cost, 1);
END;
$$ LANGUAGE plpgsql;

-- 快速启动完成！
-- 包含：5个核心AI模型，基础用户和积分系统
-- 适合：快速原型开发和测试
