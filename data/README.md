# 数据库初始化脚本

## 快速开始

### 新项目完整初始化
```bash
psql -d your_database -f data/setup.sql
```

### 快速原型开发
```bash
psql -d your_database -f data/quick-start.sql
```

## 文件说明

### 主要文件

- **`setup.sql`** - 完整的数据库初始化脚本
  - 包含所有表结构、索引、函数、视图
  - 14个AI模型（文本/图像/视频/多模态）
  - 完整的多语言支持
  - 用户管理、订单系统、积分系统
  - 适合生产环境

- **`quick-start.sql`** - 精简版快速启动脚本
  - 仅包含核心功能
  - 5个常用AI模型
  - 基础用户和积分系统
  - 适合快速原型开发

### 历史文件（保留作为参考）

- `install.sql` - 原始基础表结构
- `ai-models-schema.sql` - AI模型表结构
- `ai-models-initial-data.sql` - AI模型初始数据
- `replicate-models.sql` - Replicate模型配置
- `migrations/` - 数据库迁移脚本

## 功能特性

### AI模型支持
- **文本生成**: Gemini 2.5 系列, GPT-4o Mini
- **多模态**: GPT-4o All, GPT-4o Mini All
- **图像生成**: Sora Image, GPT-4o Image, Flux 系列
- **视频生成**: Veo3 Fast, Veo3 Pro

### 多语言支持
- 支持语言: 中文、英文、日文、韩文、法文、德文、西班牙文、意大利文、葡萄牙文、俄文
- 自动回退机制
- 本地化函数: `get_localized_content()`

### 积分系统
- 按使用量计费（tokens/images/videos）
- 自动成本计算函数: `calculate_model_cost()`
- 使用记录和统计

### 实用视图
- `user_credits_usage_stats` - 用户积分使用统计
- `model_usage_stats` - 模型使用统计
- `ai_models_localized` - 多语言模型视图

## 使用建议

1. **新项目**: 使用 `setup.sql` 获得完整功能
2. **快速测试**: 使用 `quick-start.sql` 快速启动
3. **现有项目**: 参考 `migrations/` 进行渐进式升级

## 注意事项

- 确保PostgreSQL版本支持JSONB类型
- 建议在生产环境前测试所有功能
- 可根据实际需求调整模型配置和定价
